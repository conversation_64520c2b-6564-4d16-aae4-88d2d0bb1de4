import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import '../screens/login_screen.dart';
import '../screens/app_shell.dart';
import '../screens/pages/dashboard_page.dart';
import '../screens/pages/analytics_page.dart';
import '../screens/pages/users_page.dart';
import '../screens/pages/settings_page.dart';
import '../viewmodels/auth_view_model.dart';

class AppRouter {
  static GoRouter createRouter(AuthViewModel authViewModel) => GoRouter(
    initialLocation: '/',
    routerNeglect: false,
    refreshListenable: authViewModel,
    routes: [
      GoRoute(
        path: '/',
        builder: (context, state) => const AuthWrapper(),
      ),
      GoRoute(
        path: '/login',
        builder: (context, state) => const LoginScreen(),
      ),
      ShellRoute(
        builder: (context, state, child) => AppShell(child: child),
        routes: [
          GoRoute(
            path: '/dashboard',
            builder: (context, state) => const DashboardPage(),
          ),
          GoRoute(
            path: '/analytics',
            builder: (context, state) => const AnalyticsPage(),
          ),
          GoRoute(
            path: '/users',
            builder: (context, state) => const UsersPage(),
          ),
          GoRoute(
            path: '/settings',
            builder: (context, state) => const SettingsPage(),
          ),
        ],
      ),
    ],
    // Remove redirect logic - let AuthWrapper handle navigation
  );

  // Legacy getter for backward compatibility - will be removed
  static GoRouter get router => throw UnsupportedError(
    'Use AppRouter.createRouter(authViewModel) instead'
  );
}

class AuthWrapper extends StatefulWidget {
  const AuthWrapper({super.key});

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  bool _hasNavigated = false;

  @override
  void initState() {
    super.initState();
    _initializeAuth();
  }

  Future<void> _initializeAuth() async {
    final authViewModel = Provider.of<AuthViewModel>(context, listen: false);
    await authViewModel.initialize();

    if (mounted && !_hasNavigated) {
      _navigateBasedOnAuthState(authViewModel);
    }
  }

  void _navigateBasedOnAuthState(AuthViewModel authViewModel) {
    if (_hasNavigated) return;

    _hasNavigated = true;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        if (authViewModel.isLoggedIn) {
          context.go('/dashboard');
        } else {
          context.go('/login');
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthViewModel>(
      builder: (context, authViewModel, child) {
        // Show loading while initializing
        if (!authViewModel.isInitialized) {
          return const Scaffold(
            body: Center(
              child: CircularProgressIndicator(),
            ),
          );
        }

        // Navigate once initialization is complete
        if (!_hasNavigated) {
          _navigateBasedOnAuthState(authViewModel);
        }

        return const Scaffold(
          body: Center(
            child: CircularProgressIndicator(),
          ),
        );
      },
    );
  }
}
