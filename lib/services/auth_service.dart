import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/auth_models.dart';
import 'api_service.dart';

class AuthService {
  static const String _tokenKey = 'auth_token';
  static const String _userKey = 'user_data';

  // Login method
  static Future<LoginResponse> login(LoginRequest request) async {
    try {
      final response = await ApiService.post<LoginResponse>(
        '/auth/session',
        body: request.toJson(),
        fromJson: (json) => LoginResponse.fromJson(json),
      );

      if (response.success && response.data != null) {
        final loginResponse = response.data!;
        
        // Save token and user data locally if login successful
        if (loginResponse.success && loginResponse.token != null) {
          await _saveToken(loginResponse.token!);
          if (loginResponse.user != null) {
            await _saveUser(loginResponse.user!);
          }
        }
        
        return loginResponse;
      } else {
        return LoginResponse(
          success: false,
          message: response.message ?? 'Login failed',
        );
      }
    } catch (e) {
      return LoginResponse(
        success: false,
        message: 'Login error: $e',
      );
    }
  }

  // Logout method
  static Future<bool> logout() async {
    try {
      final token = await getToken();
      
      if (token != null) {
        // Call logout API endpoint
        await ApiService.post('/auth/session', token: token);
      }
      
      // Clear local storage regardless of API call result
      await _clearToken();
      await _clearUser();
      
      return true;
    } catch (e) {
      // Clear local storage even if API call fails
      await _clearToken();
      await _clearUser();
      return false;
    }
  }

  // Check if user is logged in
  static Future<bool> isLoggedIn() async {
    final token = await getToken();
    return token != null && token.isNotEmpty;
  }

  // Get stored token
  static Future<String?> getToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_tokenKey);
    } catch (e) {
      return null;
    }
  }

  // Get stored user data
  static Future<User?> getUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userJson = prefs.getString(_userKey);
      if (userJson != null) {
        final userMap = json.decode(userJson) as Map<String, dynamic>;
        return User.fromJson(userMap);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  // Verify token with server
  static Future<bool> verifyToken() async {
    try {
      final token = await getToken();
      if (token == null) return false;

      final response = await ApiService.get('/auth/verify', token: token);
      
      if (!response.success) {
        // Token is invalid, clear local storage
        await _clearToken();
        await _clearUser();
        return false;
      }
      
      return true;
    } catch (e) {
      return false;
    }
  }

  // Private methods for local storage
  static Future<void> _saveToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_tokenKey, token);
  }

  static Future<void> _saveUser(User user) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_userKey, json.encode(user.toJson()));
  }

  static Future<void> _clearToken() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_tokenKey);
  }

  static Future<void> _clearUser() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_userKey);
  }
}
