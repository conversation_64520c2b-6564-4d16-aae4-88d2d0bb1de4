import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:vnbds_app/models/auth_models.dart';
import 'package:vnbds_app/repositories/auth_repository.dart';
import 'package:vnbds_app/viewmodels/auth_view_model.dart';

void main() {
  group('Authentication Persistence Tests', () {
    late AuthViewModel authViewModel;
    late MockAuthRepository mockAuthRepository;

    setUp(() async {
      // Initialize SharedPreferences for testing
      SharedPreferences.setMockInitialValues({});
      
      mockAuthRepository = MockAuthRepository();
      authViewModel = AuthViewModel(authRepository: mockAuthRepository);
    });

    test('should initialize as not logged in when no stored data', () async {
      // Act
      await authViewModel.initialize();

      // Assert
      expect(authViewModel.isLoggedIn, false);
      expect(authViewModel.isInitialized, true);
      expect(authViewModel.currentUser, null);
    });

    test('should restore login state from storage on initialization', () async {
      // Arrange - Simulate stored login data
      final testUser = User(
        id: '1',
        email: '<EMAIL>',
        name: 'Test User',
      );
      
      // Mock the repository to return stored data
      mockAuthRepository._isLoggedIn = true;
      mockAuthRepository._currentUser = testUser;
      mockAuthRepository._token = 'test_token';

      // Act
      await authViewModel.initialize();

      // Assert
      expect(authViewModel.isLoggedIn, true);
      expect(authViewModel.isInitialized, true);
      expect(authViewModel.currentUser?.email, '<EMAIL>');
      expect(authViewModel.currentUser?.name, 'Test User');
    });

    test('should handle login and persist data', () async {
      // Arrange
      const email = '<EMAIL>';
      const password = 'password';

      // Act
      final success = await authViewModel.login(email, password);

      // Assert
      expect(success, true);
      expect(authViewModel.isLoggedIn, true);
      expect(authViewModel.currentUser?.email, email);
    });

    test('should handle logout and clear data', () async {
      // Arrange - First login
      await authViewModel.login('<EMAIL>', 'password');
      expect(authViewModel.isLoggedIn, true);

      // Act
      final success = await authViewModel.logout();

      // Assert
      expect(success, true);
      expect(authViewModel.isLoggedIn, false);
      expect(authViewModel.currentUser, null);
    });

    test('should handle invalid token during initialization', () async {
      // Arrange - Mock stored data but invalid token
      mockAuthRepository._isLoggedIn = true;
      mockAuthRepository._token = 'invalid_token';
      mockAuthRepository._shouldFailVerification = true;

      // Act
      await authViewModel.initialize();

      // Assert
      expect(authViewModel.isLoggedIn, false);
      expect(authViewModel.isInitialized, true);
      expect(authViewModel.currentUser, null);
    });

    test('should verify token and maintain login state', () async {
      // Arrange - Login first
      await authViewModel.login('<EMAIL>', 'password');
      expect(authViewModel.isLoggedIn, true);

      // Act
      final isValid = await authViewModel.verifyToken();

      // Assert
      expect(isValid, true);
      expect(authViewModel.isLoggedIn, true);
    });

    test('should handle token verification failure', () async {
      // Arrange - Login first
      await authViewModel.login('<EMAIL>', 'password');
      expect(authViewModel.isLoggedIn, true);
      
      // Mock token verification failure
      mockAuthRepository._shouldFailVerification = true;

      // Act
      final isValid = await authViewModel.verifyToken();

      // Assert
      expect(isValid, false);
      expect(authViewModel.isLoggedIn, false);
      expect(authViewModel.currentUser, null);
    });
  });
}

// Extended MockAuthRepository for testing
class MockAuthRepository extends IAuthRepository {
  bool _isLoggedIn = false;
  User? _currentUser;
  String? _token;
  bool _shouldFailVerification = false;

  @override
  Future<LoginResponse> login(LoginRequest request) async {
    await Future.delayed(const Duration(milliseconds: 100));
    
    if (request.email == '<EMAIL>' && request.password == 'password') {
      _isLoggedIn = true;
      _token = 'mock_token_123';
      _currentUser = User(
        id: '1',
        email: request.email,
        name: 'Demo User',
      );
      
      return LoginResponse(
        success: true,
        token: _token,
        user: _currentUser,
        message: 'Login successful',
      );
    } else {
      return LoginResponse(
        success: false,
        message: 'Invalid credentials',
      );
    }
  }

  @override
  Future<bool> logout() async {
    await Future.delayed(const Duration(milliseconds: 100));
    _isLoggedIn = false;
    _token = null;
    _currentUser = null;
    return true;
  }

  @override
  Future<bool> isLoggedIn() async {
    return _isLoggedIn && _token != null;
  }

  @override
  Future<String?> getToken() async {
    return _token;
  }

  @override
  Future<User?> getUser() async {
    return _currentUser;
  }

  @override
  Future<bool> verifyToken() async {
    await Future.delayed(const Duration(milliseconds: 100));
    if (_shouldFailVerification) {
      _isLoggedIn = false;
      _token = null;
      _currentUser = null;
      return false;
    }
    return _isLoggedIn && _token != null;
  }
}
