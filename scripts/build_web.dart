#!/usr/bin/env dart

import 'dart:io';
import 'dart:convert';

/// Build script for web that replaces environment-specific placeholders
/// Usage: dart scripts/build_web.dart [environment]
/// Environment can be: development, staging, production (default: development)

void main(List<String> args) async {
  // Parse environment argument
  String environment = 'development';
  if (args.isNotEmpty) {
    environment = args[0];
  }

  print('Building web app for environment: $environment');

  // Load environment variables
  final envFile = File('environments/.env.$environment');
  if (!await envFile.exists()) {
    print('Error: Environment file not found: ${envFile.path}');
    exit(1);
  }

  final envContent = await envFile.readAsString();
  final envVars = <String, String>{};
  
  for (final line in envContent.split('\n')) {
    final trimmed = line.trim();
    if (trimmed.isEmpty || trimmed.startsWith('#')) continue;
    
    final parts = trimmed.split('=');
    if (parts.length >= 2) {
      final key = parts[0].trim();
      final value = parts.sublist(1).join('=').trim();
      envVars[key] = value;
    }
  }

  final appName = envVars['APP_NAME'] ?? 'VNBDS';
  print('Using APP_NAME: $appName');

  // Run flutter build web
  print('Running flutter build web...');
  final buildResult = await Process.run('flutter', [
    'build',
    'web',
    '--dart-define=ENVIRONMENT=$environment',
  ]);

  if (buildResult.exitCode != 0) {
    print('Flutter build failed:');
    print(buildResult.stdout);
    print(buildResult.stderr);
    exit(1);
  }

  print('Flutter build completed successfully');

  // Replace placeholders in the built HTML file
  final builtIndexFile = File('build/web/index.html');
  if (!await builtIndexFile.exists()) {
    print('Error: Built index.html not found');
    exit(1);
  }

  print('Replacing placeholders in built HTML...');
  String htmlContent = await builtIndexFile.readAsString();

  // Replace APP_NAME placeholder
  htmlContent = htmlContent.replaceAll('\$APP_NAME', appName);

  await builtIndexFile.writeAsString(htmlContent);

  // Replace placeholders in the built manifest.json file
  final builtManifestFile = File('build/web/manifest.json');
  if (await builtManifestFile.exists()) {
    print('Replacing placeholders in built manifest.json...');
    String manifestContent = await builtManifestFile.readAsString();

    // Replace APP_NAME placeholder
    manifestContent = manifestContent.replaceAll('\$APP_NAME', appName);

    await builtManifestFile.writeAsString(manifestContent);
  }

  print('Build completed successfully!');
  print('APP_NAME has been set to: $appName');
  print('Built files are in: build/web/');
}
