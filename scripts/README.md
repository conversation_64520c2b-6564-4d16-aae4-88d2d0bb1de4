# Build Scripts

This directory contains build scripts for the VNBDS Flutter web application that handle environment-specific configuration.

## Overview

The build scripts automatically replace placeholders in the web files with values from environment configuration files, ensuring that the HTML title and other metadata reflect the correct APP_NAME for each environment.

## Files

- `build_web.dart` - Main Dart build script
- `build_web.bat` - Windows batch script wrapper
- `build_web.sh` - Unix/Linux shell script wrapper

## Usage

### Windows
```bash
# Build for development (default)
scripts\build_web.bat

# Build for specific environment
scripts\build_web.bat development
scripts\build_web.bat staging
scripts\build_web.bat production
```

### Unix/Linux/macOS
```bash
# Build for development (default)
./scripts/build_web.sh

# Build for specific environment
./scripts/build_web.sh development
./scripts/build_web.sh staging
./scripts/build_web.sh production
```

### Direct Dart execution
```bash
# Build for development (default)
dart scripts/build_web.dart

# Build for specific environment
dart scripts/build_web.dart development
dart scripts/build_web.dart staging
dart scripts/build_web.dart production
```

## What it does

1. Reads the environment configuration from `environments/.env.[environment]`
2. Extracts the `APP_NAME` value
3. Runs `flutter build web` with the appropriate environment
4. Replaces `$APP_NAME` placeholders in the built files:
   - `build/web/index.html` - Updates the `<title>` and `apple-mobile-web-app-title`
   - `build/web/manifest.json` - Updates the `name` and `short_name` fields

## Environment Files

Make sure you have the following environment files configured:
- `environments/.env.development`
- `environments/.env.staging`
- `environments/.env.production`

Each file should contain at least:
```
APP_NAME=Your App Name Here
API_BASE_URL=https://your-api-url.com
ENVIRONMENT=development
DEBUG_MODE=true
```

## Example

If your `environments/.env.production` contains:
```
APP_NAME=VNBDS Production
```

After running `dart scripts/build_web.dart production`, the built HTML will have:
```html
<title>VNBDS Production</title>
<meta name="apple-mobile-web-app-title" content="VNBDS Production">
```

And the manifest.json will have:
```json
{
    "name": "VNBDS Production",
    "short_name": "VNBDS Production",
    ...
}
```
